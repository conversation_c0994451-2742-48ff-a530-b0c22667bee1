const path = require('path');
const fs = require('fs');

/**
 * Get the path to the migrator documentation file for a given dependency.
 * @param {string} dependencyName - The name of the dependency.
 * @returns {string|null} The path to the documentation file, or null if not found.
 */
function getMigratorDocPath(dependencyName) {
  let sanitizedName = dependencyName;
  if (sanitizedName.startsWith('@')) {
    sanitizedName = sanitizedName.substring(1).replace(/\//g, '-');
  }

  const docPath = path.resolve(__dirname, 'docs', `${sanitizedName}.md`);

  if (fs.existsSync(docPath)) {
    return docPath;
  }

  return null;
}

module.exports = {
  getMigratorDocPath,
}; 